{"current_level": 4, "score": 7100, "high_score": 7100, "player_data": {"health": 126, "max_health": 120, "damage": 15, "speed": 7, "fire_rate": 400, "level": 6, "xp": 236, "xp_to_next_level": 757, "upgrade_points": 3, "progression_data": {"skill_tree": {"skill_points": 0, "learned_skills": {"critical_strike": 1, "multi_shot": 2, "piercing_shots": 1, "explosive_shots": 1}, "active_synergies": []}, "equipment_manager": {"equipped": {"weapon": {"equipment_type": "weapon", "name": "Annihilator", "rarity": "Common", "level": 1, "stats": {"critical_chance": 0.47}}, "armor": {"equipment_type": "armor", "name": "Fortress", "rarity": "Common", "level": 1, "stats": {"damage_reduction": 0.42, "regeneration": 5.2}}, "accessory": {"equipment_type": "accessory", "name": "Amulet", "rarity": "Epic", "level": 1, "stats": {"resource_bonus": 2.73, "xp_bonus": 1.69, "skill_cooldown": 1.03}}}, "inventory": [{"equipment_type": "armor", "name": "Vest", "rarity": "Uncommon", "level": 1, "stats": {"damage_reduction": 0.81, "health_bonus": 142, "regeneration": 8.39}}, {"equipment_type": "armor", "name": "Mail", "rarity": "Rare", "level": 1, "stats": {"speed_bonus": 7, "health_bonus": 269, "damage_reduction": 1.39}}, {"equipment_type": "weapon", "name": "Sword", "rarity": "Common", "level": 1, "stats": {"projectile_speed": 18}}, {"equipment_type": "accessory", "name": "Crystal", "rarity": "Common", "level": 1, "stats": {"xp_bonus": 1.23}}, {"equipment_type": "armor", "name": "Vest", "rarity": "Uncommon", "level": 1, "stats": {"health_bonus": 206, "speed_bonus": 8}}, {"equipment_type": "armor", "name": "Plate", "rarity": "Rare", "level": 1, "stats": {"regeneration": 16.0}}]}, "achievement_manager": {"achievements": {"first_steps": {"name": "First Steps", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "experienced": {"name": "Experienced", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "veteran": {"name": "Veteran", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "master": {"name": "Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "first_blood": {"name": "First Blood", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "slayer": {"name": "Slayer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "destroyer": {"name": "Destroyer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_hunter": {"name": "<PERSON>", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_slayer": {"name": "Boss Slayer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "survivor": {"name": "Survivor", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "untouchable": {"name": "Untouchable", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "iron_will": {"name": "Iron Will", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_student": {"name": "Skill Student", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_master": {"name": "Skill Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "well_equipped": {"name": "Well Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "fully_equipped": {"name": "Fully Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "collector": {"name": "Collector", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "hoarder": {"name": "Hoarder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "speed_runner": {"name": "Speed Runner", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "perfectionist": {"name": "Perfectionist", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "secret_finder": {"name": "Secret Finder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "lucky_shot": {"name": "Lucky Shot", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "enemy_slayer": {"name": "Enemy Slayer", "unlocked": false, "progress": 43, "achievement_type": "progressive", "max_progress": 100}, "damage_dealer": {"name": "Damage Dealer", "unlocked": false, "progress": 0, "achievement_type": "progressive", "max_progress": 10000}, "treasure_hunter": {"name": "Treasure Hunter", "unlocked": false, "progress": 9, "achievement_type": "progressive", "max_progress": 50}, "combat_master": {"name": "Combat Master", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "immortal": {"name": "<PERSON><PERSON><PERSON><PERSON>", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "master_explorer": {"name": "Master Explorer", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}}, "completed_chains": []}, "stats": {"enemies_killed": 43, "bosses_killed": 0, "levels_completed": 0, "perfect_levels": 0, "near_death_survivals": 0, "skills_learned": 0, "maxed_skills": 0, "maxed_combat_skills": 0, "equipment_equipped": 9, "full_equipment_sets": 2, "items_collected": 9, "secrets_found": 0, "max_crit_streak": 4, "current_crit_streak": 1, "fastest_level_time": Infinity, "player_level": 6, "total_damage_dealt": 0}, "regen_timer": 26}}}